"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Home() {
  const features = [
    {
      id: "product",
      icon: "📸",
      title: "Product Photoshoots",
      description: "Generate professional product images with AI-powered photography"
    },
    {
      id: "campaign",
      icon: "🎯",
      title: "Ad Campaigns",
      description: "Create compelling marketing materials and campaign visuals"
    },
    {
      id: "ai",
      icon: "⚡",
      title: "AI-Powered",
      description: "Leverage cutting-edge AI technology for creative excellence"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-20 px-6 sm:px-8 lg:px-10">
        {/* Hero Section */}
        <div className="text-center space-y-6 mb-20">
          <Badge variant="secondary" className="mb-6 px-4 py-1.5">
            AI-Powered Creative Studio
          </Badge>
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight max-w-4xl mx-auto">
            <span className="text-primary-500">Transform Your Vision</span>
            <br />
            <span>Into Visual Magic</span>
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Create stunning product photoshoots and ad campaigns with AI. 
            Turn your marketing ideas into professional visuals in minutes.
          </p>
          <div className="flex gap-4 justify-center items-center flex-wrap pt-8">
            <Button size="lg">
              Start Creating
            </Button>
            <Button variant="outline" size="lg">
              Watch Demo
            </Button>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-3 gap-8 lg:gap-10 max-w-5xl mx-auto mb-20">
          {features.map((feature) => (
            <Card key={feature.id} variant="elevated" className="text-center">
              <CardHeader>
                <div className="text-4xl mb-3">{feature.icon}</div>
                <CardTitle>{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Process Section */}
        <div className="max-w-4xl mx-auto mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 text-foreground">How It Works</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              { step: "1", title: "Upload", desc: "Upload your product images or ideas" },
              { step: "2", title: "Customize", desc: "Choose styles, backgrounds, and effects" },
              { step: "3", title: "Generate", desc: "Get professional visuals in seconds" }
            ].map((item) => (
              <div key={item.step} className="text-center">
                <div className="w-14 h-14 bg-primary-100 dark:bg-primary-900 rounded-full mx-auto mb-6 flex items-center justify-center text-primary-700 dark:text-primary-300 font-bold text-lg">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold mb-3 text-foreground">{item.title}</h3>
                <p className="text-base text-muted-foreground leading-relaxed">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card variant="elevated" className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl md:text-3xl">Ready to Transform Your Creative Process?</CardTitle>
              <CardDescription className="text-base md:text-lg mt-2">
                Join thousands of creators and marketers using Shoshin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button size="lg">Start Free Trial</Button>
                <Button size="lg" variant="outline">Book a Demo</Button>
              </div>
              <p className="text-sm text-muted-foreground mt-6">
                No credit card required • 14-day free trial
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}