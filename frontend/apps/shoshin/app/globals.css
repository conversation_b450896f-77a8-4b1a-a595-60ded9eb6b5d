@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Shoshin Creative Design System - Light Theme */
    
    /* Primary - Vibrant Purple */
    --primary-50: 274 100% 97%; /* #faf5ff */
    --primary-100: 271 100% 95%; /* #f3e8ff */
    --primary-200: 269 100% 92%; /* #e9d5ff */
    --primary-300: 269 97% 85%; /* #d8b4fe */
    --primary-400: 270 95% 75%; /* #c084fc */
    --primary-500: 271 91% 65%; /* #a855f7 */
    --primary-600: 273 84% 47%; /* #9333ea */
    --primary-700: 272 72% 47%; /* #7c3aed */
    --primary-800: 273 80% 35%; /* #6b21a8 */
    --primary-900: 274 75% 28%; /* #581c87 */
    --primary-950: 274 94% 20%; /* #3b0764 */
    --primary: 271 91% 65%; /* Main primary */
    --primary-foreground: 0 0% 100%;
    
    /* Secondary - Electric Coral */
    --secondary-50: 351 100% 97%; /* #fff1f2 */
    --secondary-100: 351 100% 94%; /* #ffe4e6 */
    --secondary-200: 351 96% 89%; /* #fecdd3 */
    --secondary-300: 353 96% 81%; /* #fda4af */
    --secondary-400: 352 95% 70%; /* #fb7185 */
    --secondary-500: 350 89% 60%; /* #f43f5e */
    --secondary-600: 347 77% 50%; /* #e11d48 */
    --secondary-700: 345 83% 41%; /* #be123c */
    --secondary-800: 343 80% 35%; /* #9f1239 */
    --secondary-900: 342 75% 30%; /* #881337 */
    --secondary-950: 343 98% 16%; /* #4c0519 */
    --secondary: 350 89% 60%; /* Main secondary */
    --secondary-foreground: 0 0% 100%;
    
    /* Accent - Electric Blue */
    --accent-50: 214 100% 97%; /* #eff6ff */
    --accent-100: 214 95% 93%; /* #dbeafe */
    --accent-200: 213 97% 87%; /* #bfdbfe */
    --accent-300: 212 96% 78%; /* #93c5fd */
    --accent-400: 213 94% 68%; /* #60a5fa */
    --accent-500: 217 91% 60%; /* #3b82f6 */
    --accent-600: 221 83% 53%; /* #2563eb */
    --accent-700: 224 76% 48%; /* #1d4ed8 */
    --accent-800: 226 71% 40%; /* #1e40af */
    --accent-900: 224 64% 32%; /* #1e3a8a */
    --accent-950: 226 75% 20%; /* #172554 */
    --accent: 217 91% 60%; /* Main accent */
    --accent-foreground: 0 0% 100%;
    --accent-orange: 24 95% 53%; /* #F97316 */
    --accent-orange-foreground: 0 0% 100%;
    --accent-blue: 217 91% 60%; /* #3B82F6 */
    --accent-blue-foreground: 0 0% 100%;
    
    /* Success - Emerald */
    --success-50: 152 81% 96%; /* #ecfdf5 */
    --success-100: 149 80% 90%; /* #d1fae5 */
    --success-200: 152 76% 80%; /* #a7f3d0 */
    --success-300: 156 72% 67%; /* #6ee7b7 */
    --success-400: 158 64% 52%; /* #34d399 */
    --success-500: 158 64% 41%; /* #10b981 */
    --success-600: 159 61% 33%; /* #059669 */
    --success-700: 160 60% 26%; /* #047857 */
    --success-800: 161 56% 21%; /* #065f46 */
    --success-900: 162 55% 17%; /* #064e3b */
    --success: 158 64% 41%; /* Main success */
    --success-foreground: 0 0% 100%;
    
    /* Warning - Amber */
    --warning-50: 48 100% 96%; /* #fffbeb */
    --warning-100: 48 96% 89%; /* #fef3c7 */
    --warning-200: 48 97% 77%; /* #fde68a */
    --warning-300: 46 97% 65%; /* #fcd34d */
    --warning-400: 43 96% 56%; /* #fbbf24 */
    --warning-500: 38 92% 50%; /* #f59e0b */
    --warning-600: 32 95% 44%; /* #d97706 */
    --warning-700: 26 90% 37%; /* #b45309 */
    --warning-800: 23 83% 31%; /* #92400e */
    --warning-900: 22 78% 26%; /* #78350f */
    --warning: 38 92% 50%; /* Main warning */
    --warning-foreground: 0 0% 100%;
    
    /* Error/Destructive - Rose */
    --error-50: 351 100% 97%; /* #fff1f2 */
    --error-100: 351 100% 94%; /* #ffe4e6 */
    --error-200: 351 96% 89%; /* #fecdd3 */
    --error-300: 353 96% 81%; /* #fda4af */
    --error-400: 352 95% 70%; /* #fb7185 */
    --error-500: 350 89% 60%; /* #f43f5e */
    --error-600: 347 77% 50%; /* #e11d48 */
    --error-700: 345 83% 41%; /* #be123c */
    --error-800: 343 80% 35%; /* #9f1239 */
    --error-900: 342 75% 30%; /* #881337 */
    --error: 350 89% 60%; /* Main error */
    --error-foreground: 0 0% 100%;
    --destructive: 350 89% 60%;
    --destructive-foreground: 0 0% 100%;
    
    /* Neutral - Gray */
    --neutral-50: 0 0% 98%; /* #fafafa */
    --neutral-100: 240 5% 96%; /* #f4f4f5 */
    --neutral-200: 240 6% 90%; /* #e4e4e7 */
    --neutral-300: 240 5% 84%; /* #d4d4d8 */
    --neutral-400: 240 5% 65%; /* #a1a1aa */
    --neutral-500: 240 4% 46%; /* #71717a */
    --neutral-600: 240 5% 34%; /* #52525b */
    --neutral-700: 240 5% 26%; /* #3f3f46 */
    --neutral-800: 240 4% 16%; /* #27272a */
    --neutral-900: 240 6% 10%; /* #18181b */
    --neutral-950: 240 10% 4%; /* #09090b */
    
    /* Base colors */
    --background: 0 0% 100%; /* #ffffff */
    --foreground: 240 6% 10%; /* #18181b */
    --card: 0 0% 100%;
    --card-foreground: 240 6% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 6% 10%;
    --surface: 0 0% 98%; /* #fafafa */
    --surface-hover: 240 5% 96%; /* #f4f4f5 */
    --muted: 240 5% 96%; /* #f4f4f5 */
    --muted-foreground: 240 5% 34%; /* #52525b */
    --border: 0 0% 0% / 0.08; /* rgba(0, 0, 0, 0.08) */
    --input: 0 0% 0% / 0.08;
    --ring: 271 91% 65%; /* Primary focus ring */
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #a855f7 0%, #c084fc 100%);
    --gradient-secondary: linear-gradient(135deg, #f43f5e 0%, #fb7185 100%);
    --gradient-accent: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
    --gradient-vibrant: linear-gradient(135deg, #a855f7 0%, #f43f5e 50%, #3b82f6 100%);
    --gradient-subtle: linear-gradient(180deg, rgba(168, 85, 247, 0.05) 0%, rgba(244, 63, 94, 0.05) 100%);
    --gradient-mesh: radial-gradient(at 40% 20%, rgba(168, 85, 247, 0.2) 0px, transparent 50%),
                     radial-gradient(at 80% 0%, rgba(244, 63, 94, 0.15) 0px, transparent 50%),
                     radial-gradient(at 0% 50%, rgba(59, 130, 246, 0.15) 0px, transparent 50%),
                     radial-gradient(at 80% 50%, rgba(251, 191, 36, 0.1) 0px, transparent 50%),
                     radial-gradient(at 0% 100%, rgba(16, 185, 129, 0.1) 0px, transparent 50%);
    
    /* Glass morphism */
    --glass-background: rgba(255, 255, 255, 0.8);
    --glass-background-subtle: rgba(255, 255, 255, 0.6);
    --glass-border: rgba(255, 255, 255, 0.3);
    --glass-backdrop: blur(12px) saturate(180%);
    
    /* Shadows */
    --shadow-primary: 0 4px 14px 0 rgba(168, 85, 247, 0.25);
    --shadow-secondary: 0 4px 14px 0 rgba(244, 63, 94, 0.25);
    --shadow-accent: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
    
    /* Border radius */
    --radius: 0.5rem; /* 8px */
    --radius-sm: 0.25rem; /* 4px */
    --radius-md: 0.75rem; /* 12px */
    --radius-lg: 1rem; /* 16px */
    --radius-xl: 1.25rem; /* 20px */
    --radius-2xl: 1.5rem; /* 24px */
  }

  .dark {
    /* Shoshin Creative Design System - Dark Theme */
    
    /* Primary - Vibrant Purple (adjusted for dark) */
    --primary-50: 274 94% 20%; /* #3b0764 */
    --primary-100: 274 75% 28%; /* #581c87 */
    --primary-200: 273 80% 35%; /* #6b21a8 */
    --primary-300: 272 72% 47%; /* #7c3aed */
    --primary-400: 273 84% 47%; /* #9333ea */
    --primary-500: 271 91% 65%; /* #a855f7 */
    --primary-600: 270 95% 75%; /* #c084fc */
    --primary-700: 269 97% 85%; /* #d8b4fe */
    --primary-800: 269 100% 92%; /* #e9d5ff */
    --primary-900: 271 100% 95%; /* #f3e8ff */
    --primary-950: 274 100% 97%; /* #faf5ff */
    --primary: 271 91% 65%; /* Main primary */
    --primary-foreground: 274 94% 20%;
    
    /* Secondary - Electric Coral (adjusted for dark) */
    --secondary-50: 343 98% 16%; /* #4c0519 */
    --secondary-100: 342 75% 30%; /* #881337 */
    --secondary-200: 343 80% 35%; /* #9f1239 */
    --secondary-300: 345 83% 41%; /* #be123c */
    --secondary-400: 347 77% 50%; /* #e11d48 */
    --secondary-500: 350 89% 60%; /* #f43f5e */
    --secondary-600: 352 95% 70%; /* #fb7185 */
    --secondary-700: 353 96% 81%; /* #fda4af */
    --secondary-800: 351 96% 89%; /* #fecdd3 */
    --secondary-900: 351 100% 94%; /* #ffe4e6 */
    --secondary-950: 351 100% 97%; /* #fff1f2 */
    --secondary: 350 89% 60%; /* Main secondary */
    --secondary-foreground: 343 98% 16%;
    
    /* Accent - Electric Blue (adjusted for dark) */
    --accent-50: 226 75% 20%; /* #172554 */
    --accent-100: 224 64% 32%; /* #1e3a8a */
    --accent-200: 226 71% 40%; /* #1e40af */
    --accent-300: 224 76% 48%; /* #1d4ed8 */
    --accent-400: 221 83% 53%; /* #2563eb */
    --accent-500: 217 91% 60%; /* #3b82f6 */
    --accent-600: 213 94% 68%; /* #60a5fa */
    --accent-700: 212 96% 78%; /* #93c5fd */
    --accent-800: 213 97% 87%; /* #bfdbfe */
    --accent-900: 214 95% 93%; /* #dbeafe */
    --accent-950: 214 100% 97%; /* #eff6ff */
    --accent: 217 91% 60%; /* Main accent */
    --accent-foreground: 226 75% 20%;
    --accent-orange: 24 95% 53%; /* #F97316 */
    --accent-orange-foreground: 240 10% 4%;
    --accent-blue: 217 91% 60%; /* #3B82F6 */
    --accent-blue-foreground: 240 10% 4%;
    
    /* Success - Emerald (adjusted for dark) */
    --success-50: 162 55% 17%; /* #064e3b */
    --success-100: 161 56% 21%; /* #065f46 */
    --success-200: 160 60% 26%; /* #047857 */
    --success-300: 159 61% 33%; /* #059669 */
    --success-400: 158 64% 41%; /* #10b981 */
    --success-500: 158 64% 52%; /* #34d399 */
    --success-600: 156 72% 67%; /* #6ee7b7 */
    --success-700: 152 76% 80%; /* #a7f3d0 */
    --success-800: 149 80% 90%; /* #d1fae5 */
    --success-900: 152 81% 96%; /* #ecfdf5 */
    --success: 158 64% 52%; /* Main success */
    --success-foreground: 162 55% 17%;
    
    /* Warning - Amber (adjusted for dark) */
    --warning-50: 22 78% 26%; /* #78350f */
    --warning-100: 23 83% 31%; /* #92400e */
    --warning-200: 26 90% 37%; /* #b45309 */
    --warning-300: 32 95% 44%; /* #d97706 */
    --warning-400: 38 92% 50%; /* #f59e0b */
    --warning-500: 43 96% 56%; /* #fbbf24 */
    --warning-600: 46 97% 65%; /* #fcd34d */
    --warning-700: 48 97% 77%; /* #fde68a */
    --warning-800: 48 96% 89%; /* #fef3c7 */
    --warning-900: 48 100% 96%; /* #fffbeb */
    --warning: 43 96% 56%; /* Main warning */
    --warning-foreground: 22 78% 26%;
    
    /* Error/Destructive - Rose (adjusted for dark) */
    --error-50: 343 98% 16%; /* #4c0519 */
    --error-100: 342 75% 30%; /* #881337 */
    --error-200: 343 80% 35%; /* #9f1239 */
    --error-300: 345 83% 41%; /* #be123c */
    --error-400: 347 77% 50%; /* #e11d48 */
    --error-500: 350 89% 60%; /* #f43f5e */
    --error-600: 352 95% 70%; /* #fb7185 */
    --error-700: 353 96% 81%; /* #fda4af */
    --error-800: 351 96% 89%; /* #fecdd3 */
    --error-900: 351 100% 94%; /* #ffe4e6 */
    --error: 350 89% 60%; /* Main error */
    --error-foreground: 343 98% 16%;
    --destructive: 350 89% 60%;
    --destructive-foreground: 343 98% 16%;
    
    /* Neutral - Gray (inverted for dark) */
    --neutral-50: 240 10% 4%; /* #09090b */
    --neutral-100: 240 6% 10%; /* #18181b */
    --neutral-200: 240 4% 16%; /* #27272a */
    --neutral-300: 240 5% 26%; /* #3f3f46 */
    --neutral-400: 240 5% 34%; /* #52525b */
    --neutral-500: 240 4% 46%; /* #71717a */
    --neutral-600: 240 5% 65%; /* #a1a1aa */
    --neutral-700: 240 5% 84%; /* #d4d4d8 */
    --neutral-800: 240 6% 90%; /* #e4e4e7 */
    --neutral-900: 240 5% 96%; /* #f4f4f5 */
    --neutral-950: 0 0% 98%; /* #fafafa */
    
    /* Base colors */
    --background: 240 10% 4%; /* #09090b */
    --foreground: 0 0% 98%; /* #fafafa */
    --card: 240 6% 10%; /* #18181b */
    --card-foreground: 0 0% 98%;
    --popover: 240 6% 10%;
    --popover-foreground: 0 0% 98%;
    --surface: 240 6% 10%; /* #18181b */
    --surface-hover: 240 4% 16%; /* #27272a */
    --muted: 240 4% 16%; /* #27272a */
    --muted-foreground: 240 5% 84%; /* #d4d4d8 */
    --border: 255 255% 255% / 0.08; /* rgba(255, 255, 255, 0.08) */
    --input: 255 255% 255% / 0.08;
    --ring: 270 95% 75%; /* Primary focus ring */
    
    /* Gradients (adjusted for dark) */
    --gradient-primary: linear-gradient(135deg, #9333ea 0%, #a855f7 100%);
    --gradient-secondary: linear-gradient(135deg, #e11d48 0%, #f43f5e 100%);
    --gradient-accent: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    --gradient-vibrant: linear-gradient(135deg, #9333ea 0%, #e11d48 50%, #2563eb 100%);
    --gradient-subtle: linear-gradient(180deg, rgba(192, 132, 252, 0.1) 0%, rgba(251, 113, 133, 0.1) 100%);
    --gradient-mesh: radial-gradient(at 40% 20%, rgba(192, 132, 252, 0.15) 0px, transparent 50%),
                     radial-gradient(at 80% 0%, rgba(251, 113, 133, 0.1) 0px, transparent 50%),
                     radial-gradient(at 0% 50%, rgba(96, 165, 250, 0.1) 0px, transparent 50%),
                     radial-gradient(at 80% 50%, rgba(251, 191, 36, 0.08) 0px, transparent 50%),
                     radial-gradient(at 0% 100%, rgba(52, 211, 153, 0.08) 0px, transparent 50%);
    
    /* Glass morphism (dark) */
    --glass-background: rgba(24, 24, 27, 0.8);
    --glass-background-subtle: rgba(24, 24, 27, 0.6);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-backdrop: blur(12px) saturate(180%);
    
    /* Shadows (dark) */
    --shadow-primary: 0 4px 14px 0 rgba(192, 132, 252, 0.25);
    --shadow-secondary: 0 4px 14px 0 rgba(251, 113, 133, 0.25);
    --shadow-accent: 0 4px 14px 0 rgba(96, 165, 250, 0.25);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: var(--font-geist-sans), Inter, system-ui, -apple-system, BlinkMacSystemFont,
      "Segoe UI", Roboto, sans-serif;
  }

  code,
  pre {
    @apply font-mono;
    font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Consolas, 
      "Liberation Mono", Menlo, monospace;
  }
  
  /* Selection colors */
  ::selection {
    @apply bg-primary-500/20 text-foreground;
  }
  
  /* Focus outline */
  :focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-background;
  }
  
  /* Scrollbar styling */
  ::-webkit-scrollbar {
    @apply h-2 w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-neutral-100 dark:bg-neutral-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply rounded-full bg-neutral-300 dark:bg-neutral-600;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400 dark:bg-neutral-500;
  }
}

/* ReactFlow edge animations */
@keyframes dash {
  to {
    stroke-dashoffset: 10;
  }
}

.react-flow__edge-path {
  animation: dash 1s linear infinite;
}

/* Sidebar overlay styles */
.main-content-overlay {
  z-index: 40 !important;
}

@layer components {
  /* Glass morphism components */
  .glass {
    background: var(--glass-background);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    @apply shadow-lg;
  }
  
  .glass-subtle {
    background: var(--glass-background-subtle);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
  }
  
  /* Gradient text utility */
  .gradient-text {
    @apply bg-gradient-primary bg-clip-text text-transparent;
  }
  
  .gradient-text-secondary {
    @apply bg-gradient-secondary bg-clip-text text-transparent;
  }
  
  .gradient-text-accent {
    @apply bg-gradient-accent bg-clip-text text-transparent;
  }
  
  /* Gradient backgrounds */
  .gradient-primary {
    background: var(--gradient-primary);
  }
  
  .gradient-secondary {
    background: var(--gradient-secondary);
  }
  
  .gradient-accent {
    background: var(--gradient-accent);
  }
  
  .gradient-vibrant {
    background: var(--gradient-vibrant);
  }
  
  .gradient-subtle {
    background: var(--gradient-subtle);
  }
  
  .gradient-mesh {
    background: var(--gradient-mesh);
  }
  
  /* Enhanced shadows */
  .shadow-glow {
    box-shadow: var(--shadow-primary);
  }
  
  .shadow-glow-secondary {
    box-shadow: var(--shadow-secondary);
  }
  
  .shadow-glow-accent {
    box-shadow: var(--shadow-accent);
  }
  
  /* Card components */
  .card-gradient {
    @apply relative overflow-hidden rounded-xl border border-neutral-200 bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl dark:border-neutral-800 dark:bg-neutral-900;
  }
  
  .card-gradient::before {
    content: '';
    @apply absolute inset-0 -z-10 opacity-0 transition-opacity duration-300;
    background: var(--gradient-subtle);
  }
  
  .card-gradient:hover::before {
    @apply opacity-100;
  }
  
  /* Button base styles */
  .btn-base {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .btn-primary {
    @apply btn-base bg-primary-500 text-white hover:bg-primary-600 focus-visible:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn-base bg-secondary-500 text-white hover:bg-secondary-600 focus-visible:ring-secondary-500;
  }
  
  .btn-ghost {
    @apply btn-base bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800;
  }
  
  .btn-gradient {
    @apply btn-base gradient-primary text-white shadow-lg hover:shadow-xl;
  }
  
  /* Interactive elements */
  .interactive-scale {
    @apply transition-transform duration-200 ease-out hover:scale-[1.02];
  }
  
  .interactive-glow {
    @apply transition-shadow duration-300 hover:shadow-glow;
  }
  
  /* Loading states */
  .skeleton {
    @apply animate-pulse rounded-md bg-neutral-200 dark:bg-neutral-800;
  }
  
  /* Shimmer effect */
  .shimmer {
    @apply relative overflow-hidden;
  }
  
  .shimmer::before {
    content: '';
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent;
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }
}

@layer utilities {
  /* Background patterns */
  .bg-grid {
    background-image: linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .dark .bg-grid {
    background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }
  
  .bg-dots {
    background-image: radial-gradient(circle, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .dark .bg-dots {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }
  
  
  /* Gradient borders */
  .gradient-border {
    @apply relative;
  }
  
  .gradient-border::before {
    content: '';
    @apply absolute -inset-[1px] rounded-[inherit] bg-gradient-primary;
  }
  
  .gradient-border > * {
    @apply relative bg-background;
  }
  
  /* Text balance */
  .text-balance {
    text-wrap: balance;
  }
}