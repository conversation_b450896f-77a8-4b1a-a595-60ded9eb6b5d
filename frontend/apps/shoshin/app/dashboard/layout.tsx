"use client"

import { CollapsibleSidebar } from "@/components/dashboard/CollapsibleSidebar"
import { ReactNode } from "react"

interface DashboardLayoutProps {
  children: ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="flex h-screen bg-background">
      {/* Collapsible Sidebar */}
      <CollapsibleSidebar />

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  )
}
