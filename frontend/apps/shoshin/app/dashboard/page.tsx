"use client"

import { MetricsGrid } from "@/components/dashboard/MetricCard"
import { NavigationTabs } from "@/components/dashboard/NavigationTabs"
import { WelcomeSection } from "@/components/dashboard/WelcomeSection"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useState } from "react"

export default function DashboardPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("workflows")
  const [activeMetric, setActiveMetric] = useState<string>()

  const tabs = [
    { id: "workflows", label: "Workflows", count: 0 },
    { id: "credentials", label: "Credentials", count: 0 },
    { id: "executions", label: "Executions", count: 0 }
  ]

  const handleCreateWorkflow = () => {
    router.push("/editor")
  }

  const handleMetricClick = (metric: string) => {
    setActiveMetric(metric)
    // Add metric filtering logic here
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="flex h-16 items-center px-8">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center text-white font-bold text-sm">
              S
            </div>
            <h1 className="text-xl font-semibold text-foreground">Shoshin</h1>
          </div>
          <div className="ml-auto">
            <Button
              onClick={handleCreateWorkflow}
              size="default"
            >
              Create Workflow
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-8 py-10">

        {/* Metrics Cards */}
        <MetricsGrid
          className="mb-10"
          activeMetric={activeMetric}
          onMetricClick={handleMetricClick}
        />

        {/* Navigation Tabs */}
        <NavigationTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-10"
        />

        {/* Welcome Section */}
        <WelcomeSection
          userName="User"
          onCreateWorkflow={handleCreateWorkflow}
        />
      </div>
    </div>
  )
}