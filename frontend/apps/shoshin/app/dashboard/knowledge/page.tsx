"use client"

import { CreateKnowledgeBaseModal } from "@/components/knowledge/CreateKnowledgeBaseModal"
import { EmptyKnowledgeState } from "@/components/knowledge/EmptyKnowledgeState"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { useKnowledgeStore } from "@/stores/knowledgeStore"
import { BookOpen, Clock, FileText, Plus, Search } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

export default function KnowledgePage() {
  const {
    getAllKnowledgeBases,
    searchKnowledgeBases,
    searchQuery,
    setSearchQuery,
    isCreateModalOpen,
    openCreateModal,
    closeCreateModal
  } = useKnowledgeStore()

  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  
  const allKnowledgeBases = getAllKnowledgeBases()
  const filteredKnowledgeBases = localSearchQuery 
    ? searchKnowledgeBases(localSearchQuery)
    : allKnowledgeBases

  const handleSearch = (query: string) => {
    setLocalSearchQuery(query)
    setSearchQuery(query)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getDocumentStats = (knowledgeBase: { documents: Array<{ status: string }> }) => {
    const totalDocs = knowledgeBase.documents.length
    const readyDocs = knowledgeBase.documents.filter((doc) => doc.status === 'ready').length
    const processingDocs = knowledgeBase.documents.filter((doc) => doc.status === 'processing').length
    
    return { totalDocs, readyDocs, processingDocs }
  }

  if (allKnowledgeBases.length === 0) {
    return (
      <div className="h-full flex flex-col bg-background">
        {/* Header */}
        <div className="flex items-center justify-between p-8 border-b border-border">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-primary-500 rounded-md flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-3xl font-bold">Knowledge Base</h1>
          </div>
          <Button onClick={openCreateModal} size="lg">
            <Plus className="w-4 h-4 mr-2" />
            Create Knowledge Base
          </Button>
        </div>

        {/* Empty State */}
        <div className="flex-1 flex items-center justify-center">
          <EmptyKnowledgeState onCreateClick={openCreateModal} />
        </div>

        {/* Create Modal */}
        <CreateKnowledgeBaseModal
          isOpen={isCreateModalOpen}
          onClose={closeCreateModal}
        />
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-background via-background to-muted/10">
      {/* Header */}
      <div className="flex items-center justify-between p-8 border-b border-border">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gradient-primary rounded-creative flex items-center justify-center shadow-glow">
            <BookOpen className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-3xl font-bold gradient-text">Knowledge Base</h1>
        </div>
        <Button onClick={openCreateModal} variant="accent" size="lg" className="shadow-glow">
          <Plus className="w-4 h-4 mr-2" />
          Create Knowledge Base
        </Button>
      </div>

      {/* Search Bar */}
      <div className="p-8 border-b border-border">
        <div className="relative max-w-lg">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
          <Input
            placeholder="Search knowledge bases..."
            value={localSearchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-12 h-12 text-base"
          />
        </div>
      </div>

      {/* Knowledge Bases Grid */}
      <div className="flex-1 p-8 overflow-auto">
        {filteredKnowledgeBases.length === 0 ? (
          <div className="text-center py-20">
            <div className="w-20 h-20 bg-neutral-100 dark:bg-neutral-800 rounded-md mx-auto mb-6 flex items-center justify-center">
              <Search className="w-10 h-10 text-primary-500" />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-3">
              No knowledge bases found
            </h3>
            <p className="text-muted-foreground text-lg max-w-md mx-auto">
              Try adjusting your search terms or create a new knowledge base to get started.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredKnowledgeBases.map((knowledgeBase) => {
              const stats = getDocumentStats(knowledgeBase)

              return (
                <Link key={knowledgeBase.id} href={`/dashboard/knowledge/${knowledgeBase.id}`}>
                  <Card className="h-full transition-all duration-300 cursor-pointer hover:border-primary-500">
                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-xl font-bold text-foreground truncate mb-2">
                            {knowledgeBase.name}
                          </CardTitle>
                          {knowledgeBase.description && (
                            <CardDescription className="text-muted-foreground line-clamp-2 text-base">
                              {knowledgeBase.description}
                            </CardDescription>
                          )}
                        </div>
                        <div className="ml-3 flex-shrink-0">
                          <Badge variant="outline" className="text-xs">
                            {stats.totalDocs} docs
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        {/* Document Status */}
                        <div className="flex items-center space-x-6 text-sm">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-success" />
                            <span className="text-foreground font-medium">
                              {stats.readyDocs} ready
                            </span>
                          </div>
                          {stats.processingDocs > 0 && (
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-warning rounded-full animate-pulse" />
                              <span className="text-foreground font-medium">
                                {stats.processingDocs} processing
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Chunking Configuration */}
                        <div className="text-sm text-muted-foreground bg-neutral-100 dark:bg-neutral-800 rounded-md px-3 py-2">
                          Chunk: {knowledgeBase.minChunkSize}-{knowledgeBase.maxChunkSize} chars
                          {knowledgeBase.overlapSize > 0 && `, ${knowledgeBase.overlapSize} overlap`}
                        </div>

                        {/* Last Updated */}
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Clock className="w-4 h-4" />
                          <span>Updated {formatDate(knowledgeBase.updatedAt)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </div>
        )}
      </div>

      {/* Create Modal */}
      <CreateKnowledgeBaseModal 
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
      />
    </div>
  )
}
