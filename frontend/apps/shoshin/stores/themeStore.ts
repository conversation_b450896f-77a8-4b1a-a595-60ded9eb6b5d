"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";

export type Theme = "light" | "dark" | "system";
export type ResolvedTheme = "light" | "dark";

interface ThemeState {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  isLoading: boolean;

  // Actions
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  initializeTheme: () => void;
}

// Helper function to get system theme preference
const getSystemTheme = (): ResolvedTheme => {
  if (typeof window === "undefined") return "light";
  return window.matchMedia("(prefers-color-scheme: dark)").matches
    ? "dark"
    : "light";
};

// Helper function to resolve theme
const resolveTheme = (theme: Theme): ResolvedTheme => {
  return theme === "system" ? getSystemTheme() : theme;
};

// Helper function to apply theme to document
const applyThemeToDocument = (resolvedTheme: ResolvedTheme) => {
  if (typeof window === "undefined") return;

  const root = document.documentElement;
  root.classList.remove("light", "dark");
  root.classList.add(resolvedTheme);

  // Also set data attribute for compatibility
  root.setAttribute("data-theme", resolvedTheme);
};

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      theme: "system",
      resolvedTheme: "light",
      isLoading: true,

      setTheme: (theme: Theme) => {
        const resolvedTheme = resolveTheme(theme);

        set({ theme, resolvedTheme });
        applyThemeToDocument(resolvedTheme);

        // Listen for system theme changes if theme is 'system'
        if (theme === "system") {
          const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
          const handleChange = () => {
            const newResolvedTheme = getSystemTheme();
            set({ resolvedTheme: newResolvedTheme });
            applyThemeToDocument(newResolvedTheme);
          };

          // Remove existing listener if any
          mediaQuery.removeEventListener("change", handleChange);
          // Add new listener
          mediaQuery.addEventListener("change", handleChange);
        }
      },

      toggleTheme: () => {
        const { resolvedTheme } = get();
        const newTheme = resolvedTheme === "dark" ? "light" : "dark";
        get().setTheme(newTheme);
      },

      initializeTheme: () => {
        const { theme } = get();
        const resolvedTheme = resolveTheme(theme);

        set({ resolvedTheme, isLoading: false });
        applyThemeToDocument(resolvedTheme);

        // Set up system theme listener if needed
        if (theme === "system") {
          const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
          const handleChange = () => {
            const newResolvedTheme = getSystemTheme();
            set({ resolvedTheme: newResolvedTheme });
            applyThemeToDocument(newResolvedTheme);
          };

          mediaQuery.addEventListener("change", handleChange);
        }
      },
    }),
    {
      name: "theme",
      partialize: (state) => ({ theme: state.theme }),
    },
  ),
);

// Hook for easy theme access
export const useTheme = () => {
  const { theme, resolvedTheme, setTheme, toggleTheme } = useThemeStore();

  return {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
  };
};
