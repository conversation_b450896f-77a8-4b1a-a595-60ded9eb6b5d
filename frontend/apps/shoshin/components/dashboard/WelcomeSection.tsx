"use client"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface WelcomeSectionProps {
  userName?: string
  className?: string
  onCreateWorkflow?: () => void
}

export function WelcomeSection({ userName = "User", className, onCreateWorkflow }: WelcomeSectionProps) {
  const templates = [
    {
      id: "product",
      icon: "📸",
      title: "Product Shoot",
      description: "Professional product photography"
    },
    {
      id: "campaign",
      icon: "🎯",
      title: "Ad Campaign",
      description: "Marketing visuals that convert"
    },
    {
      id: "social",
      icon: "✨",
      title: "Social Media",
      description: "Eye-catching social content"
    }
  ]

  return (
    <div className={cn("py-16", className)}>
      <div className="text-center mb-12">
        <h2 className="text-2xl font-semibold mb-3 text-foreground">Get Started</h2>
        <p className="text-base text-muted-foreground">
          Choose a template or start from scratch
        </p>
      </div>

      {/* Template cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto mb-12">
        {templates.map((template) => (
          <Card
            key={template.id}
            variant="outline"
            className="cursor-pointer hover:border-primary-500 transition-colors"
            onClick={onCreateWorkflow}
          >
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-4">{template.icon}</div>
              <h3 className="text-lg font-semibold mb-2 text-foreground">{template.title}</h3>
              <p className="text-sm text-muted-foreground">
                {template.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Start from scratch */}
      <div className="text-center">
        <Button
          variant="outline"
          onClick={onCreateWorkflow}
        >
          <svg
            className="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
          Build from scratch
        </Button>
      </div>
    </div>
  )
}