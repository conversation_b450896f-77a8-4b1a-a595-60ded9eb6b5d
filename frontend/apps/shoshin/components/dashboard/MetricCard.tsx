"use client"

import { Card, CardContent, CardDescription, CardHeader } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface MetricCardProps {
  title: string
  timeRange: string
  value: string | number
  unit?: string
  deviation?: number
  deviationUnit?: string
  className?: string
  isActive?: boolean
  onClick?: () => void
}

export function MetricCard({
  title,
  timeRange,
  value,
  unit,
  deviation,
  deviationUnit,
  className,
  isActive = false,
  onClick
}: MetricCardProps) {
  const getDeviationColor = (deviation: number) => {
    if (deviation > 0) return "text-success-500 dark:text-success-400"
    if (deviation < 0) return "text-error-500 dark:text-error-400"
    return "text-muted-foreground"
  }

  const getDeviationIcon = (deviation: number) => {
    if (deviation > 0) return "↗"
    if (deviation < 0) return "↘"
    return "→"
  }

  return (
    <Card
      variant={isActive ? "elevated" : "default"}
      className={cn(
        "cursor-pointer transition-all",
        isActive && "ring-1 ring-primary-500",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-4 space-y-1.5">
        <CardDescription className="text-xs font-semibold uppercase tracking-wider text-muted-foreground">
          {title}
        </CardDescription>
        <CardDescription className="text-xs text-muted-foreground">
          {timeRange}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-baseline justify-between">
          <div className="text-2xl font-bold text-foreground">
            {value}
            {unit && <span className="text-sm font-normal text-muted-foreground ml-1">{unit}</span>}
          </div>
          {deviation !== undefined && deviation !== null && (
            <div className={cn(
              "flex items-center text-xs font-semibold",
              getDeviationColor(deviation)
            )}>
              <span className="mr-1">{getDeviationIcon(deviation)}</span>
              <span>
                {Math.abs(deviation)}{deviationUnit || '%'}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface MetricsGridProps {
  className?: string
  activeMetric?: string
  onMetricClick?: (metric: string) => void
}

export function MetricsGrid({ className, activeMetric, onMetricClick }: MetricsGridProps) {
  const metrics = [
    {
      id: "total",
      title: "Creative Projects",
      timeRange: "Last 7 days",
      value: 12,
      deviation: 3
    },
    {
      id: "failed",
      title: "AI Generations",
      timeRange: "Last 7 days",
      value: 847,
      deviation: 156
    },
    {
      id: "failureRate",
      title: "Success Rate",
      timeRange: "Last 7 days",
      value: 98.2,
      unit: "%",
      deviation: 2.1
    },
    {
      id: "timeSaved",
      title: "Time Saved",
      timeRange: "Last 7 days",
      value: 24,
      unit: "hrs",
      deviation: 8
    },
    {
      id: "runtime",
      title: "Avg. Generation",
      timeRange: "Last 7 days",
      value: 3.2,
      unit: "s",
      deviation: -0.5
    }
  ]

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-5 gap-6", className)}>
      {metrics.map((metric) => (
        <MetricCard
          key={metric.id}
          title={metric.title}
          timeRange={metric.timeRange}
          value={metric.value}
          unit={metric.unit}
          deviation={metric.deviation}
          isActive={activeMetric === metric.id}
          onClick={() => onMetricClick?.(metric.id)}
        />
      ))}
    </div>
  )
}
