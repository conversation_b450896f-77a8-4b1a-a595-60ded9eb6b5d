"use client"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface Tab {
  id: string
  label: string
  count?: number
}

interface NavigationTabsProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}

export function NavigationTabs({ tabs, activeTab, onTabChange, className }: NavigationTabsProps) {
  return (
    <div className={cn("border-b", className)}>
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "py-3 px-1 text-sm font-medium transition-colors flex items-center gap-2 border-b-2",
              activeTab === tab.id
                ? "border-primary-500 text-foreground"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-neutral-300 dark:hover:border-neutral-700"
            )}
          >
            {tab.label}
            {tab.count !== undefined && (
              <Badge
                variant="outline"
                className="text-xs"
              >
                {tab.count}
              </Badge>
            )}
          </button>
        ))}
      </nav>
    </div>
  )
}
