"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useKnowledgeStore, type KnowledgeBase } from "@/stores/knowledgeStore"
import { AlertTriangle } from "lucide-react"

interface DeleteKnowledgeBaseModalProps {
  isOpen: boolean
  onClose: () => void
  knowledgeBase: KnowledgeBase
}

export function DeleteKnowledgeBaseModal({ 
  isOpen, 
  onClose, 
  knowledgeBase 
}: DeleteKnowledgeBaseModalProps) {
  const router = useRouter()
  const { deleteKnowledgeBase, isLoading } = useKnowledgeStore()
  const [confirmationText, setConfirmationText] = useState("")
  
  const isConfirmationValid = confirmationText === knowledgeBase.name

  const handleDelete = async () => {
    if (!isConfirmationValid) return
    
    try {
      await deleteKnowledgeBase(knowledgeBase.id)
      onClose()
      router.push('/dashboard/knowledge')
    } catch (error) {
      console.error("Failed to delete knowledge base:", error)
    }
  }

  const handleClose = () => {
    setConfirmationText("")
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <DialogTitle className="text-red-600 dark:text-red-400">
              Delete Knowledge Base
            </DialogTitle>
          </div>
          <DialogDescription className="text-left">
            This action cannot be undone. This will permanently delete the knowledge base
            <span className="font-semibold"> &quot;{knowledgeBase.name}&quot; </span>
            and all of its documents.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning Box */}
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-red-800 dark:text-red-200 mb-1">
                  This will permanently delete:
                </p>
                <ul className="text-red-700 dark:text-red-300 space-y-1">
                  <li>• {knowledgeBase.documents.length} document(s)</li>
                  <li>• All processed chunks and embeddings</li>
                  <li>• Knowledge base configuration</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="space-y-2">
            <Label htmlFor="confirmation" className="text-sm font-medium">
              Type <span className="font-mono bg-gray-100 dark:bg-gray-800 px-1 rounded">
                {knowledgeBase.name}
              </span> to confirm:
            </Label>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={knowledgeBase.name}
              className="font-mono"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            variant="destructive"
            onClick={handleDelete}
            disabled={!isConfirmationValid || isLoading}
          >
            {isLoading ? "Deleting..." : "Delete Knowledge Base"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
