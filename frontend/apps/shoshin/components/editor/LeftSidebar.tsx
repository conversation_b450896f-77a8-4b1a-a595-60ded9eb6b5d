"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import {
  Brain,
  ChevronLeft,
  Code,
  Database,
  GitBranch,
  Globe,
  MessageSquare,
  Package,
  RotateCcw,
  Route,
  Search,
  User,
  Workflow
} from "lucide-react"
import { useState } from "react"
import { BlockItem } from "./BlockItem"

const blockCategories = [
  {
    title: "Blocks",
    items: [
      { id: "agent", name: "Agent", description: "Build an agent", icon: User, color: "bg-primary-500" },
      { id: "api", name: "API", description: "Use any API", icon: Globe, color: "bg-accent-500" },
      { id: "condition", name: "Condition", description: "Add a condition", icon: GitBranch, color: "bg-orange-500" },
      { id: "function", name: "Function", description: "Run custom logic", icon: Code, color: "bg-secondary-500" },
      { id: "router", name: "Router", description: "Route workflow", icon: Route, color: "bg-green-500" },
      { id: "memory", name: "Memory", description: "Add memory store", icon: Database, color: "bg-pink-500" },
      { id: "knowledge", name: "Knowledge", description: "Use vector search", icon: Brain, color: "bg-teal-500" },
      { id: "workflow", name: "Workflow", description: "Execute another workflow", icon: Workflow, color: "bg-amber-500" },
      { id: "response", name: "Response", description: "Send structured API response", icon: MessageSquare, color: "bg-blue-500" },
      { id: "loop", name: "Loop", description: "Iterate over items", icon: RotateCcw, color: "bg-cyan-500" }
    ]
  },
  {
    title: "Tools",
    items: []
  }
]

export function LeftSidebar() {
  const [activeTab, setActiveTab] = useState("Blocks")
  const [isCollapsed, setIsCollapsed] = useState(false)

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  return (
    <TooltipProvider>
      <div
        className={cn(
          "bg-background border-r border-border flex flex-col transition-all duration-300 ease-in-out relative z-0",
          isCollapsed ? "w-16" : "w-64"
        )}
      >
        {isCollapsed ? (
          // Collapsed state - show single icon
          <div className="flex flex-col items-center justify-center h-full">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSidebar}
                  className="w-12 h-12 text-muted-foreground hover:text-foreground hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                >
                  <Package className="w-6 h-6" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                Open Blocks & Tools
              </TooltipContent>
            </Tooltip>
          </div>
        ) : (
          // Expanded state - show full sidebar
          <>
            {/* Header with collapse button */}
            <div className="p-4 border-b border-border">
              <div className="flex items-center justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSidebar}
                  className="text-muted-foreground hover:text-foreground hover:bg-neutral-100 dark:hover:bg-neutral-800 p-1 h-8 w-8"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Search */}
            <div className="p-4 border-b border-border">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full bg-neutral-100 dark:bg-neutral-800 text-foreground placeholder-muted-foreground pl-10 pr-3 py-2 rounded-md border border-border focus:border-primary-500 focus:outline-none text-sm transition-colors"
                />
              </div>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-border">
              {blockCategories.map((category) => (
                <button
                  key={category.title}
                  onClick={() => setActiveTab(category.title)}
                  className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                    activeTab === category.title
                      ? "text-foreground bg-neutral-100 dark:bg-neutral-800 border-b-2 border-primary-500"
                      : "text-muted-foreground hover:text-foreground"
                  }`}
                >
                  {category.title}
                </button>
              ))}
            </div>

            {/* Block List */}
            <div className="flex-1 overflow-y-auto p-4 space-y-2">
              {blockCategories
                .find((cat) => cat.title === activeTab)
                ?.items.map((block) => (
                  <BlockItem
                    key={block.id}
                    id={block.id}
                    name={block.name}
                    description={block.description}
                    icon={block.icon}
                    color={block.color}
                  />
                ))}
            </div>

            {/* Bottom section */}
            <div className="p-4 border-t border-border">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleSidebar}
                className="w-full text-muted-foreground hover:text-foreground hover:bg-neutral-100 dark:hover:bg-neutral-800 justify-start"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Close Toolbar
              </Button>
            </div>
          </>
        )}
      </div>
    </TooltipProvider>
  )
}
