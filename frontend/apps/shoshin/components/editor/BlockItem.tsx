"use client"

import { LucideIcon } from "lucide-react"

interface BlockItemProps {
  id: string
  name: string
  description: string
  icon: LucideIcon
  color: string
}

export function BlockItem({ id, name, description, icon: Icon, color }: BlockItemProps) {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("application/reactflow", JSON.stringify({
      type: id,
      name,
      description
    }))
    e.dataTransfer.effectAllowed = "move"
  }

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      className="flex items-center p-3 rounded-md bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 cursor-grab active:cursor-grabbing transition-all duration-200 group border border-transparent hover:border-primary-500/30"
    >
      {/* Icon */}
      <div className={`w-10 h-10 ${color} rounded-md flex items-center justify-center mr-3 flex-shrink-0 shadow-sm group-hover:shadow-md transition-shadow`}>
        <Icon className="w-5 h-5 text-white" />
      </div>
      
      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="text-sm font-semibold text-foreground">
          {name}
        </div>
        {description && (
          <div className="text-xs text-muted-foreground truncate mt-0.5">
            {description}
          </div>
        )}
      </div>
    </div>
  )
}
