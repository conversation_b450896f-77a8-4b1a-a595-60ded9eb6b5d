"use client"

import { BaseEdge, type EdgeProps, getSmoothStepPath } from "@xyflow/react"

export const ShoshinEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
}: EdgeProps) => {
  const isHorizontal = sourcePosition === 'right' || sourcePosition === 'left'

  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 8,
    offset: isHorizontal ? 30 : 20,
  })

  const markerId = `shoshin-arrow-${id}`

  return (
    <g>
      <defs>
        <marker
          id={markerId}
          viewBox="0 0 12 12"
          refX="11"
          refY="6"
          markerWidth="8"
          markerHeight="8"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <path
            d="M2,2 L10,6 L2,10 L4,6 z"
            fill="#6366f1"
            stroke="#6366f1"
            strokeWidth="1"
          />
        </marker>
      </defs>
      <BaseEdge
        path={edgePath}
        style={{
          strokeWidth: 2,
          stroke: '#6366f1',
          strokeDasharray: '5,5',
          ...style,
        }}
        markerEnd={`url(#${markerId})`}
      />
    </g>
  )
}
