"use client"

import { LeftSidebar } from "./LeftSidebar"
import { MainCanvas } from "./MainCanvas"
import { OutermostSidebar } from "./OutermostSidebar"
import { RightSidebar } from "./RightSidebar"
import { TopToolbar } from "./TopToolbar"

export function EditorLayout() {
  return (
    <div className="h-full w-full flex flex-col bg-gradient-to-br from-background via-background to-muted/5">
      {/* Top Toolbar */}
      <TopToolbar />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Outermost Sidebar - Fixed positioned, overlays on top */}
        <OutermostSidebar />

        {/* Content area with left margin to account for outermost sidebar */}
        <div className="flex-1 flex" style={{ marginLeft: '80px' }}>
          {/* Tools/Blocks Sidebar - Adjacent to outermost sidebar space */}
          <LeftSidebar />

          {/* Main Canvas */}
          <div className="flex-1 relative bg-dots">
            <MainCanvas />
          </div>

          {/* Right Sidebar */}
          <RightSidebar />
        </div>
      </div>
    </div>
  )
}
