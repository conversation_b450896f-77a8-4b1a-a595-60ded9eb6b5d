"use client"

import { useState } from "react"
import { MessageSquare, Terminal, Variable, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

const tabs = [
  { id: "chat", label: "Chat", icon: MessageSquare },
  { id: "console", label: "Console", icon: Terminal },
  { id: "variables", label: "Variables", icon: Variable },
]

export function RightSidebar() {
  const [activeTab, setActiveTab] = useState("chat")
  const [isCollapsed, setIsCollapsed] = useState(false)

  if (isCollapsed) {
    return (
      <div className="w-12 bg-background border-l border-border flex flex-col items-center py-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(false)}
          className="text-muted-foreground hover:text-foreground"
        >
          <MessageSquare className="w-4 h-4" />
        </Button>
      </div>
    )
  }

  return (
    <div className="w-80 bg-background border-l border-border flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="text-sm font-medium text-foreground">Select output sources</div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(true)}
          className="text-muted-foreground hover:text-foreground"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-border">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? "text-foreground bg-neutral-100 dark:bg-neutral-800 border-b-2 border-primary-500"
                : "text-muted-foreground hover:text-foreground"
            }`}
          >
            <tab.icon className="w-4 h-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 p-4">
        {activeTab === "chat" && (
          <div className="h-full flex flex-col">
            <div className="flex-1 flex items-center justify-center text-muted-foreground text-sm">
              No messages yet
            </div>
            <div className="mt-4">
              <input
                type="text"
                placeholder="Type a message..."
                className="w-full bg-neutral-100 dark:bg-neutral-800 text-foreground placeholder-muted-foreground px-3 py-2 rounded-md border border-border focus:border-primary-500 focus:outline-none text-sm"
              />
            </div>
          </div>
        )}

        {activeTab === "console" && (
          <div className="h-full">
            <div className="text-muted-foreground text-sm">Console output will appear here...</div>
          </div>
        )}

        {activeTab === "variables" && (
          <div className="h-full">
            <div className="text-muted-foreground text-sm">Variables will be listed here...</div>
          </div>
        )}
      </div>
    </div>
  )
}
