import { cn } from './utils'

export const typography = {
  // Display headings
  display: {
    large: "text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight",
    medium: "text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight",
    small: "text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight",
  },
  
  // Page headings
  heading: {
    h1: "text-3xl md:text-4xl font-bold tracking-tight",
    h2: "text-2xl md:text-3xl font-bold tracking-tight",
    h3: "text-xl md:text-2xl font-semibold",
    h4: "text-lg md:text-xl font-semibold",
    h5: "text-base md:text-lg font-semibold",
    h6: "text-sm md:text-base font-semibold",
  },
  
  // Body text
  body: {
    large: "text-lg leading-relaxed",
    default: "text-base leading-relaxed",
    small: "text-sm leading-relaxed",
    tiny: "text-xs leading-relaxed",
  },
  
  // UI text
  ui: {
    label: "text-sm font-medium",
    caption: "text-xs text-muted-foreground",
    helper: "text-sm text-muted-foreground",
    button: "text-sm font-medium",
    badge: "text-xs font-medium",
  },
  
  // Code/mono
  code: {
    large: "font-mono text-base",
    default: "font-mono text-sm",
    small: "font-mono text-xs",
  },
  
  // Utility function to apply typography classes
  apply: (variant: string, className?: string) => {
    const parts = variant.split('.')
    let classes = typography as any
    
    for (const part of parts) {
      classes = classes[part]
      if (!classes) return className || ''
    }
    
    return cn(classes, className)
  }
}

// Spacing system
export const spacing = {
  // Page layout
  page: {
    x: "px-4 sm:px-6 lg:px-8",
    y: "py-6 sm:py-8 lg:py-10",
    section: "py-12 sm:py-16 lg:py-20",
  },
  
  // Container widths
  container: {
    xs: "max-w-3xl",
    sm: "max-w-4xl",
    md: "max-w-5xl",
    lg: "max-w-6xl",
    xl: "max-w-7xl",
    full: "max-w-full",
  },
  
  // Component spacing
  component: {
    xs: "p-2",
    sm: "p-3",
    md: "p-4",
    lg: "p-6",
    xl: "p-8",
  },
  
  // Gap spacing
  gap: {
    xs: "gap-1",
    sm: "gap-2",
    md: "gap-4",
    lg: "gap-6",
    xl: "gap-8",
    "2xl": "gap-10",
  },
}

// Shadow system
export const shadows = {
  xs: "shadow-xs",
  sm: "shadow-sm",
  md: "shadow-md",
  lg: "shadow-lg",
  xl: "shadow-xl",
  "2xl": "shadow-2xl",
  none: "shadow-none",
  
  // Interactive shadows
  interactive: {
    default: "shadow-sm hover:shadow-md transition-shadow",
    elevated: "shadow-md hover:shadow-lg transition-shadow",
    pressed: "shadow-none",
  },
  
  // Colored shadows
  primary: "shadow-md shadow-primary-500/20",
  secondary: "shadow-md shadow-secondary-500/20",
  accent: "shadow-md shadow-accent-500/20",
}

// Border radius system
export const radius = {
  none: "rounded-none",
  sm: "rounded-sm",
  md: "rounded-md",
  lg: "rounded-lg",
  xl: "rounded-xl",
  "2xl": "rounded-2xl",
  "3xl": "rounded-3xl",
  full: "rounded-full",
}

// Animation system
export const animations = {
  // Transition durations
  duration: {
    fast: "duration-150",
    normal: "duration-300",
    slow: "duration-500",
    slower: "duration-700",
  },
  
  // Transition timing
  timing: {
    linear: "ease-linear",
    in: "ease-in",
    out: "ease-out",
    inOut: "ease-in-out",
  },
  
  // Common transitions
  all: "transition-all",
  colors: "transition-colors",
  opacity: "transition-opacity",
  shadow: "transition-shadow",
  transform: "transition-transform",
}