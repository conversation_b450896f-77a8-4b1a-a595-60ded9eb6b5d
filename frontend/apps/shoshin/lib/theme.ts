export const theme = {
  colors: {
    light: {
      // Primary - Vibrant Purple/Violet
      primary: {
        50: '#faf5ff',
        100: '#f3e8ff',
        200: '#e9d5ff',
        300: '#d8b4fe',
        400: '#c084fc',
        500: '#a855f7', // Main primary
        600: '#9333ea',
        700: '#7c3aed',
        800: '#6b21a8',
        900: '#581c87',
        950: '#3b0764',
      },
      // Secondary - Electric Coral
      secondary: {
        50: '#fff1f2',
        100: '#ffe4e6',
        200: '#fecdd3',
        300: '#fda4af',
        400: '#fb7185',
        500: '#f43f5e', // Main secondary
        600: '#e11d48',
        700: '#be123c',
        800: '#9f1239',
        900: '#881337',
        950: '#4c0519',
      },
      // Accent - Electric Blue
      accent: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6', // Main accent
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
        950: '#172554',
      },
      // Success - Emerald
      success: {
        50: '#ecfdf5',
        100: '#d1fae5',
        200: '#a7f3d0',
        300: '#6ee7b7',
        400: '#34d399',
        500: '#10b981',
        600: '#059669',
        700: '#047857',
        800: '#065f46',
        900: '#064e3b',
      },
      // Warning - Amber
      warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f',
      },
      // Error - Rose
      error: {
        50: '#fff1f2',
        100: '#ffe4e6',
        200: '#fecdd3',
        300: '#fda4af',
        400: '#fb7185',
        500: '#f43f5e',
        600: '#e11d48',
        700: '#be123c',
        800: '#9f1239',
        900: '#881337',
      },
      // Neutral - Gray
      neutral: {
        50: '#fafafa',
        100: '#f4f4f5',
        200: '#e4e4e7',
        300: '#d4d4d8',
        400: '#a1a1aa',
        500: '#71717a',
        600: '#52525b',
        700: '#3f3f46',
        800: '#27272a',
        900: '#18181b',
        950: '#09090b',
      },
      // Background & Surface
      background: '#ffffff',
      surface: '#fafafa',
      surfaceHover: '#f4f4f5',
      // Text
      text: {
        primary: '#18181b',
        secondary: '#52525b',
        tertiary: '#71717a',
        inverse: '#fafafa',
      },
      // Borders
      border: {
        default: 'rgba(0, 0, 0, 0.08)',
        hover: 'rgba(0, 0, 0, 0.12)',
        focus: '#a855f7',
      },
    },
    dark: {
      // Primary - Vibrant Purple/Violet (adjusted for dark mode)
      primary: {
        50: '#3b0764',
        100: '#581c87',
        200: '#6b21a8',
        300: '#7c3aed',
        400: '#9333ea',
        500: '#a855f7', // Main primary
        600: '#c084fc',
        700: '#d8b4fe',
        800: '#e9d5ff',
        900: '#f3e8ff',
        950: '#faf5ff',
      },
      // Secondary - Electric Coral (adjusted for dark mode)
      secondary: {
        50: '#4c0519',
        100: '#881337',
        200: '#9f1239',
        300: '#be123c',
        400: '#e11d48',
        500: '#f43f5e', // Main secondary
        600: '#fb7185',
        700: '#fda4af',
        800: '#fecdd3',
        900: '#ffe4e6',
        950: '#fff1f2',
      },
      // Accent - Electric Blue (adjusted for dark mode)
      accent: {
        50: '#172554',
        100: '#1e3a8a',
        200: '#1e40af',
        300: '#1d4ed8',
        400: '#2563eb',
        500: '#3b82f6', // Main accent
        600: '#60a5fa',
        700: '#93c5fd',
        800: '#bfdbfe',
        900: '#dbeafe',
        950: '#eff6ff',
      },
      // Success - Emerald
      success: {
        50: '#064e3b',
        100: '#065f46',
        200: '#047857',
        300: '#059669',
        400: '#10b981',
        500: '#34d399',
        600: '#6ee7b7',
        700: '#a7f3d0',
        800: '#d1fae5',
        900: '#ecfdf5',
      },
      // Warning - Amber
      warning: {
        50: '#78350f',
        100: '#92400e',
        200: '#b45309',
        300: '#d97706',
        400: '#f59e0b',
        500: '#fbbf24',
        600: '#fcd34d',
        700: '#fde68a',
        800: '#fef3c7',
        900: '#fffbeb',
      },
      // Error - Rose
      error: {
        50: '#4c0519',
        100: '#881337',
        200: '#9f1239',
        300: '#be123c',
        400: '#e11d48',
        500: '#f43f5e',
        600: '#fb7185',
        700: '#fda4af',
        800: '#fecdd3',
        900: '#ffe4e6',
      },
      // Neutral - Gray
      neutral: {
        50: '#09090b',
        100: '#18181b',
        200: '#27272a',
        300: '#3f3f46',
        400: '#52525b',
        500: '#71717a',
        600: '#a1a1aa',
        700: '#d4d4d8',
        800: '#e4e4e7',
        900: '#f4f4f5',
        950: '#fafafa',
      },
      // Background & Surface
      background: '#09090b',
      surface: '#18181b',
      surfaceHover: '#27272a',
      // Text
      text: {
        primary: '#fafafa',
        secondary: '#d4d4d8',
        tertiary: '#a1a1aa',
        inverse: '#18181b',
      },
      // Borders
      border: {
        default: 'rgba(255, 255, 255, 0.08)',
        hover: 'rgba(255, 255, 255, 0.12)',
        focus: '#c084fc',
      },
    },
  },
  // Typography
  typography: {
    fonts: {
      sans: 'var(--font-geist-sans), system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      mono: 'var(--font-geist-mono), ui-monospace, SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
      display: 'var(--font-geist-sans), system-ui, sans-serif',
    },
    sizes: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem', // 60px
      '7xl': '4.5rem',  // 72px
    },
    weights: {
      thin: '100',
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900',
    },
    lineHeights: {
      none: '1',
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
      loose: '2',
    },
  },
  // Spacing
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',  // 2px
    1: '0.25rem',     // 4px
    1.5: '0.375rem',  // 6px
    2: '0.5rem',      // 8px
    2.5: '0.625rem',  // 10px
    3: '0.75rem',     // 12px
    3.5: '0.875rem',  // 14px
    4: '1rem',        // 16px
    5: '1.25rem',     // 20px
    6: '1.5rem',      // 24px
    7: '1.75rem',     // 28px
    8: '2rem',        // 32px
    9: '2.25rem',     // 36px
    10: '2.5rem',     // 40px
    11: '2.75rem',    // 44px
    12: '3rem',       // 48px
    14: '3.5rem',     // 56px
    16: '4rem',       // 64px
    20: '5rem',       // 80px
    24: '6rem',       // 96px
    28: '7rem',       // 112px
    32: '8rem',       // 128px
  },
  // Border Radius
  borderRadius: {
    none: '0',
    sm: '0.25rem',    // 4px
    default: '0.5rem', // 8px
    md: '0.75rem',    // 12px
    lg: '1rem',       // 16px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '2rem',    // 32px
    full: '9999px',
  },
  // Shadows
  shadows: {
    light: {
      none: 'none',
      xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      sm: '0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.03)',
      default: '0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -1px rgba(0, 0, 0, 0.04)',
      md: '0 6px 10px -1px rgba(0, 0, 0, 0.08), 0 3px 6px -1px rgba(0, 0, 0, 0.05)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
      // Colored shadows
      primary: '0 4px 14px 0 rgba(168, 85, 247, 0.25)',
      secondary: '0 4px 14px 0 rgba(244, 63, 94, 0.25)',
      accent: '0 4px 14px 0 rgba(59, 130, 246, 0.25)',
    },
    dark: {
      none: 'none',
      xs: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
      sm: '0 2px 4px -1px rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.2)',
      default: '0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.3)',
      md: '0 6px 10px -1px rgba(0, 0, 0, 0.6), 0 3px 6px -1px rgba(0, 0, 0, 0.4)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.7), 0 4px 6px -2px rgba(0, 0, 0, 0.5)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.8), 0 10px 10px -5px rgba(0, 0, 0, 0.6)',
      '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.9)',
      inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.4)',
      // Colored shadows
      primary: '0 4px 14px 0 rgba(192, 132, 252, 0.25)',
      secondary: '0 4px 14px 0 rgba(251, 113, 133, 0.25)',
      accent: '0 4px 14px 0 rgba(96, 165, 250, 0.25)',
    },
  },
  // Blur effects
  blur: {
    none: '0',
    sm: '4px',
    default: '8px',
    md: '12px',
    lg: '16px',
    xl: '24px',
    '2xl': '40px',
    '3xl': '64px',
  },
  // Glassmorphism
  glass: {
    light: {
      background: 'rgba(255, 255, 255, 0.8)',
      backgroundSubtle: 'rgba(255, 255, 255, 0.6)',
      border: 'rgba(255, 255, 255, 0.3)',
      backdrop: 'blur(12px) saturate(180%)',
    },
    dark: {
      background: 'rgba(24, 24, 27, 0.8)',
      backgroundSubtle: 'rgba(24, 24, 27, 0.6)',
      border: 'rgba(255, 255, 255, 0.1)',
      backdrop: 'blur(12px) saturate(180%)',
    },
  },
  // Gradients
  gradients: {
    // Primary gradients
    primary: {
      light: 'linear-gradient(135deg, #a855f7 0%, #c084fc 100%)',
      dark: 'linear-gradient(135deg, #9333ea 0%, #a855f7 100%)',
    },
    // Secondary gradients
    secondary: {
      light: 'linear-gradient(135deg, #f43f5e 0%, #fb7185 100%)',
      dark: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 100%)',
    },
    // Accent gradients
    accent: {
      light: 'linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%)',
      dark: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
    },
    // Mixed gradients
    vibrant: {
      light: 'linear-gradient(135deg, #a855f7 0%, #f43f5e 50%, #3b82f6 100%)',
      dark: 'linear-gradient(135deg, #9333ea 0%, #e11d48 50%, #2563eb 100%)',
    },
    // Subtle background gradients
    subtle: {
      light: 'linear-gradient(180deg, rgba(168, 85, 247, 0.05) 0%, rgba(244, 63, 94, 0.05) 100%)',
      dark: 'linear-gradient(180deg, rgba(192, 132, 252, 0.1) 0%, rgba(251, 113, 133, 0.1) 100%)',
    },
    // Mesh gradients
    mesh: {
      light: `radial-gradient(at 40% 20%, rgba(168, 85, 247, 0.2) 0px, transparent 50%),
              radial-gradient(at 80% 0%, rgba(244, 63, 94, 0.15) 0px, transparent 50%),
              radial-gradient(at 0% 50%, rgba(59, 130, 246, 0.15) 0px, transparent 50%),
              radial-gradient(at 80% 50%, rgba(251, 191, 36, 0.1) 0px, transparent 50%),
              radial-gradient(at 0% 100%, rgba(16, 185, 129, 0.1) 0px, transparent 50%)`,
      dark: `radial-gradient(at 40% 20%, rgba(192, 132, 252, 0.15) 0px, transparent 50%),
             radial-gradient(at 80% 0%, rgba(251, 113, 133, 0.1) 0px, transparent 50%),
             radial-gradient(at 0% 50%, rgba(96, 165, 250, 0.1) 0px, transparent 50%),
             radial-gradient(at 80% 50%, rgba(251, 191, 36, 0.08) 0px, transparent 50%),
             radial-gradient(at 0% 100%, rgba(52, 211, 153, 0.08) 0px, transparent 50%)`,
    },
  },
  // Animations
  animations: {
    durations: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
      slower: '700ms',
    },
    easings: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    },
  },
  // Z-index scale
  zIndices: {
    base: 0,
    dropdown: 10,
    sticky: 20,
    fixed: 30,
    modal: 40,
    popover: 50,
    tooltip: 60,
    notification: 70,
  },
};

// Type definitions
export type Theme = typeof theme;
export type ColorMode = 'light' | 'dark';
export type Colors = typeof theme.colors.light;
export type Typography = typeof theme.typography;
export type Spacing = typeof theme.spacing;
export type BorderRadius = typeof theme.borderRadius;
export type Shadows = typeof theme.shadows.light;
export type Gradients = typeof theme.gradients;