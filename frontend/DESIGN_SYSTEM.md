# Shoshin Design System

## Overview
A vibrant, modern design system created for marketers, designers, and creators using AI-powered creative tools.

## Design Philosophy
- **Vibrant & Energetic**: Bold colors that inspire creativity
- **Modern & Clean**: Minimal yet attractive with subtle depth  
- **Professional**: Sophisticated enough for business use
- **Playful Elements**: Subtle animations and interactions

## Color Palette

### Primary Colors
- **Primary (Purple)**: `#a855f7` - Creative, innovative
- **Secondary (Coral)**: `#f43f5e` - Energetic, modern  
- **Accent (Blue)**: `#3b82f6` - Trust, tech-forward

### Supporting Colors
- **Success**: `#10b981` - Emerald green
- **Warning**: `#f59e0b` - Amber
- **Error**: `#f43f5e` - Rose

### Theme Support
- Full dark and light mode support
- Automatic theme detection
- Manual theme switching

## Design Elements

### Typography
- Font Stack: Inter, system-ui, -apple-system
- Display Font: Cal Sans
- Mono Font: JetBrains Mono

### Components

#### Buttons
- Variants: default, gradient, glass, outline, ghost, secondary
- Sizes: sm, default, lg, xl
- Hover effects with scale and shadow

#### Cards  
- Variants: default, gradient, glass, elevated, outline, glow, mesh
- Interactive hover states
- Subtle animations

#### Badges
- Variants: default, gradient, glass, outline
- Rounded pill shape
- Scale animation on hover

#### Inputs
- Variants: default, glass, gradient, underline, filled
- Focus states with ring
- Multiple sizes

### Effects

#### Gradients
- Primary gradient (purple to pink)
- Secondary gradient (coral to pink)
- Accent gradient (blue to cyan)
- Vibrant gradient (multi-color)
- Mesh gradient backgrounds

#### Glass Morphism
- Frosted glass effect
- Backdrop blur
- Semi-transparent backgrounds

#### Shadows
- Colored shadows matching brand colors
- Glow effects for interactive elements
- Elevation shadows for depth

#### Animations
- Fade in/out
- Slide (up, down, left, right)
- Scale in/out
- Bounce in
- Shimmer loading
- Pulse effects

## Implementation

### File Structure
```
/lib/theme.ts          - Core theme configuration
/lib/theme-utils.ts    - Theme utilities
/app/globals.css       - CSS variables and utilities
/tailwind.config.ts    - Tailwind configuration
/hooks/use-theme.ts    - Theme switching hook
```

### Usage Examples

```tsx
// Button
<Button variant="gradient" size="lg">
  Create Magic
</Button>

// Card with glass effect
<Card variant="glass">
  <CardContent>...</CardContent>
</Card>

// Badge with animation
<Badge variant="pulse">New</Badge>

// Input with gradient
<Input variant="gradient" placeholder="Enter text" />
```

## Best Practices

1. Use semantic color names (primary, secondary) rather than specific colors
2. Leverage CSS variables for theme switching
3. Apply hover states for interactive elements
4. Use animations sparingly for important interactions
5. Maintain consistent spacing using the defined scale
6. Test both light and dark modes

## Accessibility

- High contrast ratios for text
- Focus states for all interactive elements
- Reduced motion options respected
- Semantic HTML structure
- ARIA labels where needed